<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    <title>Squatch Studios</title>
    <meta name="description" content="Synthetic Talent and Location" />
    <meta
      name="keywords"
      content="ai film, ai video, media production, character development, synthetic talent, synthetic location"
    />
    <meta name="author" content="Squatch Studios" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://squatch.film" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Squatch Studios" />
    <meta property="og:description" content="Synthetic Talent and Location" />
    <meta property="og:url" content="https://squatch.film" />
    <meta property="og:site_name" content="Squatch Studios" />
    <meta
      property="og:image"
      content="https://squatch.film/asset/poppy_cover.jpg"
    />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta
      property="og:image:alt"
      content="Squatch Studios - Synthetic Talent and Location"
    />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Squatch Studios" />
    <meta name="twitter:description" content="Synthetic Talent and Location" />
    <meta
      name="twitter:image"
      content="https://squatch.film/asset/poppy_cover.jpg"
    />
    <meta
      name="twitter:image:alt"
      content="Squatch Studios - Synthetic Talent and Location"
    />

    <!-- Additional Meta Tags -->
    <meta name="theme-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link
      rel="icon"
      type="image/png"
      sizes="192x192"
      href="/android-chrome-192x192.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="512x512"
      href="/android-chrome-512x512.png"
    />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Oswald:wght@200..700&display=swap"
      rel="stylesheet"
    />
    <noscript>
      <style>
        :root {
          font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
          line-height: 1.5;
          font-weight: 400;
          color-scheme: light dark;
          color: rgba(255, 255, 255, 0.87);
          background-color: #000000;
          font-synthesis: none;
          text-rendering: optimizeLegibility;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        * {
          box-sizing: border-box;
        }

        body {
          margin: 0;
          min-width: 320px;
          min-height: 100vh;
          background-color: #000000;
        }

        .noscript-body {
          display: block;
          width: 100%;
        }

        a {
          font-weight: 500;
          text-decoration: inherit;
        }

        a:hover {
          color: #535bf2;
        }

        .app-container {
          color: #ffffff;
          user-select: none;
          font-family: "Oswald", sans-serif;
          padding-top: 96px;
        }

        .header {
          height: 96px;
          background-color: #000000;
          box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5);
          z-index: 9999;
          position: fixed;
          left: 0;
          right: 0;
          top: 0;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          padding-left: 36px;
        }

        .logo {
          height: 72px;
          width: 206px;
          background-image: url("/asset/sslogo.png");
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center left;
        }

        .cover {
          height: 640px;
          width: 100%;
          background-size: cover;
          background-position: center;
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-bottom: 4px;
        }

        .cover-interview {
          background-image: url("/asset/interview_cover.jpg");
          justify-content: flex-start;
        }

        .cover-poppy {
          background-image: url("/asset/poppy_cover.jpg");
          justify-content: flex-end;
        }

        .overlay-title {
          display: flex;
          flex-direction: column;
          justify-content: center;
          max-width: 50%;
        }

        .overlay-title-end {
          align-items: flex-end;
        }

        .overlay-title-start {
          align-items: flex-start;
        }

        .introducing {
          font-size: 36px;
          letter-spacing: 0.1em;
          line-height: 1;
        }

        .poppy-carter {
          font-size: 96px;
          letter-spacing: 0.1em;
          line-height: 1;
        }

        .poppy-carter-right {
          font-size: 96px;
          letter-spacing: 0.1em;
          line-height: 1;
          text-align: right;
        }

        .youtube-link {
          font-size: 36px;
          margin-top: 64px;
          margin-left: -16px;
          letter-spacing: 0.1em;
          line-height: 1;
          padding: 16px 16px;
          cursor: pointer;
          color: #ffffff;
        }

        .youtube-link-blue:hover {
          background-color: #2591bd;
        }

        .youtube-link-orange:hover {
          background-color: #ed7b1a;
        }

        .details {
          width: 100%;
          padding-left: 36px;
          padding-bottom: 96px;
          font-size: 24px;
          font-weight: 400;
        }

        .email {
          color: #ed7b1a;
          cursor: pointer;
        }

        .email:hover {
          color: #ffffff;
          background-color: #ed7b1a;
        }

        /* Mobile styles */
        @media (max-width: 768px) {
          .app-container {
            padding-top: 72px;
          }

          .header {
            height: 72px;
            padding-left: 16px;
          }

          .logo {
            height: 48px;
          }

          .cover {
            align-items: flex-end;
            height: 560px;
          }

          .overlay-title {
            max-width: 90%;
            padding-right: 0px;
          }

          .introducing {
            font-size: 24px;
          }

          .poppy-carter {
            font-size: 52px;
            margin-right: 8px;
          }

          .poppy-carter-right {
            font-size: 52px;
            margin-left: 8px;
            text-align: right;
          }

          .youtube-link {
            font-size: 24px;
            margin-top: 24px;
            margin-bottom: 16px;
          }

          .youtube-link-blue {
            background-color: #2591bd;
          }

          .youtube-link-orange {
            background-color: #ed7b1a;
          }

          .details {
            font-size: 20px;
            padding-left: 16px;
          }
        }

        @media (prefers-color-scheme: light) {
          :root {
            color: #213547;
            background-color: #ffffff;
          }

          a:hover {
            color: #747bff;
          }
        }
      </style>
    </noscript>
  </head>
  <body>
    <div id="root"></div>
    <noscript>
      <div class="noscript-body">
        <div class="app-container">
          <div class="header">
            <div class="logo"></div>
          </div>
          <div class="cover cover-interview">
            <div class="overlay-title overlay-title-end">
              <div class="poppy-carter-right">POPPY CARTER</div>
              <div class="introducing">INTERVIEW</div>
              <a href="https://youtu.be/rATtYB5T9lU" target="_blank">
                <div class="youtube-link youtube-link-blue">
                  WATCH ON YOUTUBE
                </div>
              </a>
            </div>
          </div>
          <div class="cover cover-poppy">
            <div class="overlay-title overlay-title-start">
              <div class="introducing">INTRODUCING</div>
              <div class="poppy-carter">POPPY CARTER</div>
              <a href="https://youtu.be/gCrHmChqbVw" target="_blank">
                <div class="youtube-link youtube-link-orange">
                  WATCH ON YOUTUBE
                </div>
              </a>
            </div>
          </div>
          <div class="details">
            <p>Website coming soon.</p>
            <p>
              Enquiries —
              <a href="mailto:<EMAIL>">
                <span class="email"><EMAIL></span>
              </a>
            </p>
          </div>
        </div>
      </div>
    </noscript>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
