import styled from "styled-components";

const AppContainer = styled.div`
  user-select: none;
  font-family: "<PERSON>", sans-serif;
  color: #ffffff;
  padding-top: 96px;
  @media (max-width: 768px) {
    padding-top: 72px;
  }
`;

const Header = styled.div`
  height: 96px;
  background-color: #000000;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  display: flex;
  flex: direction: row;
  align-items: center;
  justify-content: start;
  padding-left: 36px;
  
  @media (max-width: 768px) {
    height: 72px;
    padding-left: 16px;
  }
`;

const Logo = styled.div`
  height: 72px;
  width: 206px;
  background-image: url("/asset/sslogo.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center left;
  @media (max-width: 768px) {
    height: 48px;
  }
`;
const Cover = styled.div<{ image: string, align: string }>`
  height: 640px;
  width: 100%;
  background-image: url("/asset/${(props) => props.image}.jpg");
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: ${(props) => props.align};
  margin-bottom: 4px;
  @media (max-width: 768px) {
    align-items: end;
    height: 560px;
  }
`;

const OverlayTitle = styled.div<{ align: string }>`
  flex-direction: column;
  justify-content: center;
  align-items: ${(props) => props.align};
  max-width: 50%;
  @media (max-width: 768px) {
    max-width: 90%;
  }
`;

const Introducing = styled.div`
  font-size: 36px;
  letter-spacing: 0.1em;
  line-height: 1;
  margin-to
  @media (max-width: 768px) {
    font-size: 24px;
  }
`;

const PoppyCarter = styled.div`
  font-size: 96px;
  letter-spacing: 0.1em;
  line-height: 1;
  @media (max-width: 768px) {
    font-size: 52px;
    margin-right: 8px;
  }
`;

const YoutubeLink = styled.div<{ color: string }>`
  font-size: 36px;
  margin-top: 64px;
  margin-left: -16px;
  letter-spacing: 0.1em;
  line-height: 1;
  padding: 16px 16px;
  cursor: pointer;
  color: #ffffff;
  &:hover {
    background-color: ${(props) => props.color};
  }
  @media (max-width: 768px) {
    font-size: 24px;
    margin-top: 24px;
    margin-bottom: 16px;
    background-color: ${(props) => props.color};
  }
`;

const Details = styled.div`
  width: 100%;
  padding-left: 36px;
  padding-bottom: 96px;
  font-size: 24px;
  font-weight: 400;
  @media (max-width: 768px) {
    font-size: 20px;
    padding-left: 16px;
  }
`;

const Email = styled.span`
  color: #ed7b1a;
  cursor: pointer;
  &:hover {
    color: #ffffff;
    background-color: #ed7b1a;
  }
`;

function App() {
  return (
    <AppContainer>
      <Header>
        <Logo />
      </Header>
      <Cover image="interview_cover" align="start">
        <OverlayTitle align="end">
          <PoppyCarter>POPPY CARTER</PoppyCarter>
          <Introducing>INTERVIEW</Introducing>
          <a href="https://youtu.be/Wx9zlf8RCGA" target="_blank">
            <YoutubeLink color="#2591bd">WATCH ON YOUTUBE</YoutubeLink>
          </a>
        </OverlayTitle>
      </Cover>
      <Cover image="poppy_cover" align="end">
        <OverlayTitle align="start">
          <Introducing>INTRODUCING</Introducing>
          <PoppyCarter>POPPY CARTER</PoppyCarter>
          <a href="https://youtu.be/gCrHmChqbVw" target="_blank">
            <YoutubeLink color="#ed7b1a">WATCH ON YOUTUBE</YoutubeLink>
          </a>
        </OverlayTitle>
      </Cover>
      <Details>
        <p>Website coming soon.</p>
        <p>
          Enquiries —{" "}
          <a href="mailto:<EMAIL>">
            <Email><EMAIL></Email>
          </a>
        </p>
      </Details>
    </AppContainer>
  );
}

export default App;
